#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""Create a daemon to execute queries against our Salesforce account.

Using the SalesForce Streaming API, implemented in StreamingClient,
lurk, waiting until signal is received to grab unsigned keylist from SalesForce.

When StreamingClient indicates license should be generated, interrogate SF,
and pass-through unsigned xml to XmlSigner, then return resultant signed license
to SalesForce.

"""
import hashlib
import os
import pprint
import sys
import time
from typing import Optional

import requests
from requests import HTTPError, RequestException

from CLI_guy import utils
from shared.LicensingConfig import LicensingConfig
from shared.constants import SF_API_VERSION, LicenseState, PATH_TO_CONFIG_FILE, PROJECT_ROOT, PATH_TO_EMP_CONNECTOR_JAR

try:
    import simplejson as json
except ImportError:
    import json

import subprocess
import logging.handlers
import traceback
from threading import Thread
from queue import Queue, Empty


log = logging.getLogger("resting_daemon")
key_record_log = logging.getLogger("KeyRecord")
LICENSING_CONFIG: Optional[LicensingConfig] = None


def load_config():
    global LICENSING_CONFIG
    if LICENSING_CONFIG is None:
        LICENSING_CONFIG = LicensingConfig.from_json_file(PATH_TO_CONFIG_FILE)


load_config()


def check_log_setup(log_loc, log_name):
    # need to check existence of and create any missing/required logs with correct permissions
    # We need to create one log for the licenseSigner process: licenseSigner.log
    if not os.path.isdir(log_loc):
        sys.exit(f"Could not start daemon. Attempted to initialize logging, but directory doesn't exit: {log_loc}")
    open(os.path.join(log_loc, "licenseSigner.log"), "a+")
    open(os.path.join(log_loc, log_name), "a+")


def req_from_sf(url, query=None):
    params = {"q": query} if query else {}
    try:
        response = requests.get(
            url,
            # TODO: all auth bearer? or only custom SF endpoints, the others are Oauth?
            headers={"Authorization": f"Bearer {utils.get_oauth_token(LICENSING_CONFIG)}"},
            params=params,
        )
        return response.json()
    except HTTPError as he:
        log.error(f"HTTP error occurred: {he.response.status_code} - {he.response.text}")
    except RequestException:
        log.error("Unable to read SF query response", exc_info=True)
    except ValueError as ve:
        log.error(f"Failed to parse JSON response", ve)
    return None


def get_signed_key_from_sf(key_id):
    url = f"{LICENSING_CONFIG.login_server}/services/data/v{SF_API_VERSION}/sobjects/LicenseKey__c/{key_id}"
    return req_from_sf(url)


def update_db(updated_values, license_id):
    try:
        with requests.Session() as session:
            response = session.patch(
                f"{LICENSING_CONFIG.login_server}/services/data/v{SF_API_VERSION}/sobjects/LicenseKey__c/{license_id}?_HttpMethod=PATCH",
                json=updated_values,
                timeout=10,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {utils.get_oauth_token(LICENSING_CONFIG)}",
                },
            )
            if response.status_code // 100 == 2:
                log.debug(f"Update for license with ID: {license_id} was successful.")
            else:
                log.warning(f"Update for license with ID: {license_id} was not successful.")
    except HTTPError as e:
        log.error(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
    except RequestException as e:
        log.error(f"Failed to update SF: {str(e)}")


def query_sf(sf_url, query):
    """Query Salesforce, follow record URLs, and return collected data."""
    all_data = []
    try:
        results = req_from_sf(url=sf_url, query=query)
        records = results.get("records", [])

        for record in records:
            url = record.get("attributes", {}).get("url")
            if url:
                record_data = req_from_sf(url=f'{LICENSING_CONFIG.login_server}{url}')
                all_data.append(record_data)
        return all_data
    except Exception:
        log.error("Error querying SF.", exc_info=True)
        return []


def enqueue_output(std_out, queue):
    for output_line in iter(std_out.readline, b""):
        queue.put(output_line)
    std_out.close()


def output_checksum(license_string, md5_string):
    checksum_path = os.path.join(LICENSING_CONFIG.checksum_loc, md5_string[:2], md5_string)
    log.debug(f"Checksum path: {checksum_path}")
    # check for a dir named for the first two chars of the md5, if it's not there, create it
    if not os.path.exists(os.path.dirname(checksum_path)):
        log.debug(f"Creating new directory - {md5_string[:2]}")
        os.makedirs(os.path.dirname(checksum_path))
    license_dump = open(checksum_path, "w")
    log.debug(f"Writing copy of license keyfile to {checksum_path}")
    license_dump.write(license_string)
    license_dump.close()


def validate_key(unsigned_key_obj):
    required_keys = {"Id", "State__c", "Name", "Unsigned_XML__c"}
    if not isinstance(unsigned_key_obj, dict):
        log.error("Response from SF should be a dict")
        return False

    missing_keys = required_keys - unsigned_key_obj.keys()
    if missing_keys:
        log.error(f"SF response is missing key(s): {''.join(missing_keys)}")
        return False

    unsigned_xml = unsigned_key_obj.get("Unsigned_XML__c")
    if unsigned_xml is None:
        log.warning(
            f"SF response for key: {str(unsigned_key_obj.get('Id'))} has no unsigned XML. Will not attempt to sign."
        )
        return False
    return True


def safe_process_unsigned_key(unsigned_key):
    try:
        process_unsigned_key(unsigned_key)
    except Exception as e:
        log.error(f"An error was encountered attempting to sign key:\n{pprint.pformat(unsigned_key)}", e)


def process_unsigned_key(unsigned_key_obj):
    """Validates unsigned XML. Attempts to sign. Writes checksum to disk."""
    if not validate_key(unsigned_key_obj):
        return
    license_id = str(unsigned_key_obj.get("Id", ""))
    key_name = str(unsigned_key_obj.get("Name", ""))
    unsigned_xml = unsigned_key_obj.get("Unsigned_XML__c")
    log.info(
        f'Retrieved unsigned XML from SF License - Key: {key_name}, ID: {license_id}, State: {str(unsigned_key_obj.get("State__c", ""))}'
    )

    signed_key = utils.sign_keylist(unsigned_xml)
    if not signed_key:
        log.error("Nothing returned from signer")
        return
    log.info(f"Key with ID {license_id} was processed and signed.")
    update_db({"Signed_XML__c": signed_key, "State__c": LicenseState.SIGNED_KEY_UPDATED}, license_id)

    # Generate and log checksum
    md5_string = hashlib.md5(signed_key.encode("utf-8")).hexdigest()
    key_record_log.info(f"{key_name},{license_id},Signed Key Updated,{md5_string}")
    output_checksum(signed_key, md5_string)


def process_stream_message(stream_msg):
    stream_event_msg = str(stream_msg)
    if "Awaiting Signed Key" in stream_event_msg:
        # SF located an event, parse the JSON data inside of it
        query_data = utils.convert_emp_string_to_json(stream_event_msg)
        license_key_id = query_data.get("sobject", {}).get("Id")
        if not license_key_id:
            log.warning(f"Expected Id in stream event message: {stream_event_msg}")
            return
        unsigned_keys = query_sf(
            f"{LICENSING_CONFIG.login_server}/services/data/v{SF_API_VERSION}/query",
            f"SELECT Unsigned_XML__c FROM LicenseKey__c WHERE Id='{license_key_id}' "
            f"AND State__c = '{LicenseState.VALIDATED_AWAITING_SIG}'",
        )
        if len(unsigned_keys) != 1:
            error = (
                f"Query for license with id {license_key_id} returned 0 results."
                if len(unsigned_keys) == 0
                else f"Query for license with id {license_key_id} returned {str(len(unsigned_keys))} results."
            )
            update_db({"State__c": LicenseState.ERR_INVALID_SUPPORTING_DATA, "Error_Detail__c": error}, license_key_id)
            return

        for unsigned_key in list_of_unsigned_keys:
            safe_process_unsigned_key(unsigned_key)


def start_streaming_api(url, token, max_heap_size=128):
    if not os.path.exists(PATH_TO_EMP_CONNECTOR_JAR):
        raise Exception(f"EMP Connector jar missing. Should be located at {PATH_TO_EMP_CONNECTOR_JAR}")

    log.info("Starting EMP-Connector")

    listener_args = [
        "java",
        f"-Xmx{max_heap_size}m",
        "-classpath",
        os.path.normpath(PATH_TO_EMP_CONNECTOR_JAR),
        "com.salesforce.emp.connector.example.BearerTokenExample",
        url,
        token,
        "/topic/UnsignedXML",
    ]
    on_posix = "posix" in sys.builtin_module_names
    return subprocess.Popen(
        listener_args, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, bufsize=1, text=True, close_fds=on_posix
    )


def start_listener():
    global listener
    listener = start_streaming_api(LICENSING_CONFIG.login_server, utils.get_oauth_token(LICENSING_CONFIG))
    return listener


if __name__ == "__main__":
    load_config()
    utils.setup_logger("resting_daemon", LICENSING_CONFIG.log_name, LICENSING_CONFIG)
    check_log_setup(LICENSING_CONFIG.log_loc, LICENSING_CONFIG.log_name)

    log.info("Resting Daemon Started")
    log.debug(f"Configuration information:\n {pprint.pformat(LICENSING_CONFIG)}")

    # time (in seconds from epoch) last heartbeat message was received
    time_last_hb = time.time()
    max_hb_interval = 3 * 60.0  # 3 minutes - hb normally occurs every 2 minutes
    max_restarts = 3  # how many restarts result in escalation and email notification
    restart_times = []
    elevated = False  # keep track of when email has been sent
    cur_wait_tm = 0  # time in seconds we wait
    resume_time = 0  # seconds since epoch to resume checking
    # Set our loop to look for input at the same frequency
    checkFreq = 0.5
    # loop and process output from Streaming API
    msg = ""
    try:
        listener = start_listener()
        q = Queue()
        t = Thread(target=enqueue_output, args=(listener.stdout, q))
        t.daemon = True  # thread dies with program
        t.start()
        pid = listener.pid
        log.debug(f"Listener PID: {str(pid)}")
        # setting i=1 makes the program skip the initial check for stragglers (missedSigs)
        i = 0
        missedSigFreq = 0.5 * 60.0 / checkFreq  # 30 seconds
        healthySubprocCheck = 30.0 / checkFreq  # 30 sec
        while True:
            try:
                line = q.get_nowait()
            except Empty:
                msg = ""
            else:
                msg = line.strip()
                if isinstance(msg, (bytes, bytearray)):
                    msg = msg.decode()
                if msg != "":
                    process_stream_message(msg)
            # check for heartbeat
            hb_limit = 60
            stragglerCheck = False
            if ("channel=/meta/subscribe" in msg or "channel=/meta/connect" in msg) and "successful=true" in msg:
                msg_dict = {
                    key.strip(): value.strip()
                    for entry in msg.strip("{}").split(",")
                    if "=" in entry
                    for key, value in [entry.split("=", 1)]
                }

                # StreamingAPI connections are restricted to 120 minutes...at hb 60, restart
                # TODO: remove this restart if this restriction is lifted in future releases of SF StreamingAPI
                if "id" in msg_dict.keys():
                    if int(msg_dict.get("id")) >= hb_limit:
                        log.debug(f'Heartbeat #{msg_dict.get("id")}, restarting to refresh StreamingAPI connection')
                        if listener.poll() is None:
                            try:
                                listener.kill()
                                time.sleep(5)
                            except Exception as e:
                                log.debug(f"Error terminating pid {pid}.  {str(type(e))}: {str(e)}")
                        listener = start_listener()

                        t = Thread(target=enqueue_output, args=(listener.stdout, q))
                        t.daemon = True  # thread dies with program
                        t.start()
                        i = 0
                        pid = listener.pid
                        log.debug(f"New listener PID: {str(pid)}")
                        # don't count this against restarts for elevation
                    time_last_hb = time.time()
                    # if we receive a heartbeat, start checking health again immediately
                    if elevated:
                        log.debug("Heartbeat detected, resuming heartbeat check now")
                        resume_time = 0
                    if msg_dict.get("id") in ("3", "4"):
                        # after connection is established, wait until hb number 3:
                        # successful to check for stragglers, below
                        stragglerCheck = True
            # every 'healthSubprocCheck' seconds, check the health of the listener, if it is dead, restart
            if i % healthySubprocCheck == 0:
                now = time.time()
                # if email has been sent, we are in elevated state and need to wait until resume_time
                run_check = True
                if elevated:
                    if now > resume_time:
                        log.debug(f"Waited {cur_wait_tm} minutes, now resume checking StreamingAPI health")
                        run_check = True
                    else:
                        run_check = False

                # check that last heartbeat (connection success message) was less
                # than max_heartbeat_interval seconds ago
                if now - time_last_hb > max_hb_interval and run_check:
                    log.error(
                        f"No heartbeat received from SalesForce connection in over {str(max_hb_interval)} seconds."
                    )
                    log.info("Terminating and restarting StreamingAPI Listener")
                    time_last_hb = time.time()
                    if listener.poll() is None:
                        try:
                            listener.terminate()
                            time.sleep(1)
                        except Exception as e:
                            log.debug(f"Error terminating pid {pid}.  {str(type(e))}: {str(e)}")
                    listener = start_listener()

                    t = Thread(target=enqueue_output, args=(listener.stdout, q))
                    t.daemon = True  # thread dies with program
                    t.start()
                    restart_times.append(now)
                    i = 0
                    pid = listener.pid
                    log.debug(f"New listener PID: {str(pid)}")
                # listener.poll() returns None if process is running, number if it has died
                if listener.poll() is not None and run_check:
                    log.warning(f"Status poll of StreamingAPI indicates status '{str(listener.poll())}'- not alive.")
                    log.info("Restarting StreamingAPI Listener")
                    listener = start_listener()

                    with q.mutex:
                        q.queue.clear()
                    t = Thread(target=enqueue_output, args=(listener.stdout, q))
                    t.daemon = True  # thread dies with program
                    t.start()
                    # add java-caused restart to list of restarts
                    restart_times.append(now)
                    i = 0
                    pid = listener.pid
                    log.debug(f"New listener PID: {str(pid)}")
                recent_restarts = 0
                time_deltas = 0
                threshold = 10.0  # 10 minutes ago
                thresh_time = now - threshold * 60
                if len(restart_times) > 0:
                    for restart in restart_times:
                        if restart >= thresh_time:
                            recent_restarts += 1
                            # calculate the time difference between each restart for avg time between calc
                            if restart_times.index(restart) != len(restart_times) - 1:
                                time_deltas += restart_times[restart_times.index(restart) + 1] - restart
                            if recent_restarts >= max_restarts:
                                if len(restart_times) == 1:
                                    # handles divide by 0 case
                                    avg_restarts = time_deltas
                                else:
                                    avg_restarts = time_deltas / (len(restart_times) - 1)
                                try:
                                    contact_email = LICENSING_CONFIG.username
                                except:
                                    log.debug(
                                        f"Could not pull email address "
                                        f"from username in {PATH_TO_CONFIG_FILE}, using <EMAIL>"
                                    )
                                    contact_email = "<EMAIL>"
                                subject = "SalesForce License Signer problem"
                                if not elevated:
                                    cur_wait_tm = 5  # 5 minutes
                                    log.debug(
                                        f"Now in elevated state, restart, "
                                        f"then wait {cur_wait_tm} minutes before next check"
                                    )
                                    elevated = True
                                    resume_time = now + cur_wait_tm * 60.0
                                    restart_times = []
                                    log.debug(
                                        f"Notifying {contact_email} via email that {max_restarts} "
                                        f"restarts have occurred in last half hour"
                                    )
                                    message = (
                                        f"{max_restarts} restarts have occurred in the last {threshold} minutes- "
                                        f"average time between restarts {avg_restarts} sec\n"
                                        "Will resume checking again in 5 minutes"
                                    )
                                else:
                                    new_wait_tm = 30  # 30 minutes
                                    message = (
                                        f"After waiting {cur_wait_tm} minutes, another {max_restarts} "
                                        f"restarts have occurred in less than a {threshold} min period- "
                                        f"average time between restarts {avg_restarts} sec\n Will resume "
                                        f"checking again in {new_wait_tm} minutes"
                                    )
                                    cur_wait_tm = new_wait_tm
                                    log.debug(
                                        f"We were already in elevated state, try another restart, "
                                        f"then only resume checking after {cur_wait_tm} minutes"
                                    )
                                    resume_time = now + cur_wait_tm * 60
                                    restart_times = []
                                    log.debug(
                                        f"Notifying {contact_email} that after the initial waiting period, "
                                        f"an additional {max_restarts} restarts have occurred"
                                    )
                                log.debug(f"Sending email: {message}")
                                utils.send_email(contact_email, subject, message)
                        else:
                            log.debug(f"Removing {restart} from restart_times (more than {threshold} minutes old)")
                            restart_times.remove(restart)
                elif elevated and run_check:
                    # after _threshold_ minutes of health (empty restart_times array),
                    # reset elevated state to False, email user
                    elevated = False
                    resume_time = 0
                    try:
                        # config_file = load_config_file()
                        contact_email = LICENSING_CONFIG.username
                    except:
                        log.debug(
                            f"Could not pull email address from username in {PATH_TO_CONFIG_FILE}, "
                            "using <EMAIL>"
                        )
                        contact_email = "<EMAIL>"
                    log.debug(f"Notifying {contact_email} via email that process is healthy")
                    subject = "SalesForce License Signer out of trouble"
                    message = (
                        f"Healthy heartbeats have been received for {threshold} minutes- "
                        "(we assume) everything is back to normal."
                    )
                    utils.send_email(contact_email, subject, message)

            # every 'missedSigFreq' seconds, perform rest cleanup (StreamingAPI specifies
            # that some changes/updates may be omitted) to check for missed licenses
            if (i % missedSigFreq == 0 and i != 0) or stragglerCheck:
                if stragglerCheck:
                    log.debug(
                        "Performing initial straggler query "
                        "to verify no licenses are waiting/were missed by StreamingAPI on restart"
                    )
                try:
                    # in case we are between messages, but a StreamingAPI message is on
                    # the way, perform check again after waiting 'checkFreq' seconds
                    time.sleep(checkFreq)
                    # log.debug("Check to make sure no notifications are coming in from StreamingAPI at this instant")
                    line = q.get_nowait()
                except Empty:
                    pass
                else:
                    process_stream_message(line.strip())

                list_of_unsigned_keys = query_sf(
                    f"{LICENSING_CONFIG.login_server}/services/data/v{SF_API_VERSION}/query",
                    f"SELECT Unsigned_XML__c, Id FROM LicenseKey__c WHERE State__c='{LicenseState.VALIDATED_AWAITING_SIG}'",
                )
                for unsigned in list_of_unsigned_keys:
                    safe_process_unsigned_key(unsigned)
                # In case the iterator grows large...this will reset it every year or so
                if i >= int(365.25 * 24 * 60 * 60 * 1 / checkFreq):
                    i = 0
            # each loop, sleep for 'checkFreq' seconds before executing again
            time.sleep(checkFreq)
            i += 1
            if not os.path.isfile(os.path.join(PROJECT_ROOT, "RestingDaemon", "placeholder.file")):
                if i <= 1:
                    log.info(
                        f"In order to run, this program expects "
                        f'\'{os.path.join(PROJECT_ROOT, "RestingDaemon", "placeholder.file")}\' to be present.'
                    )
                else:
                    log.debug("Placeholder file not present.  Stopping.")
                break
    except Exception as e:
        log.error(f"Stopping because of error- {traceback.format_exc().strip()}")
    finally:
        # wait a second then try to terminate our subprocess
        log.info("License Signer terminating subprocesses")
        time.sleep(1)
        listener = start_listener()
        pid = listener.pid
        log.debug(f"Verifying subprocess pid {pid} is dead")
        if listener.poll() is None:
            try:
                listener.terminate()
            except Exception as e:
                log.debug(f"Looks like that pid was already dead. {str(type(e))}: {str(e)}")
        log.info("Shutdown complete.")

        logging.shutdown()
